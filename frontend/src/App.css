.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
}

.upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.upload-area:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.upload-area.dragover {
  border-color: #007bff;
  background: linear-gradient(145deg, #f8f9ff, #e3f2fd);
  transform: scale(1.02);
}

.upload-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

.file-input {
  display: none;
}

.btn-upload {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.btn-upload:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 123, 255, 0.3);
}

.error-table {
  margin-top: 30px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.success-alert {
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 4px 6px rgba(40, 167, 69, 0.3);
}

.error-alert {
  background: linear-gradient(45deg, #dc3545, #c82333);
  border: none;
  color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 4px 6px rgba(220, 53, 69, 0.3);
}

.history-card {
  margin-bottom: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.status-badge {
  font-size: 0.8rem;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
