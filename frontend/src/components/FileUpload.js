import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, Alert, Table, Spinner } from 'react-bootstrap';
import axios from 'axios';

const FileUpload = ({ onUploadSuccess }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [validationResult, setValidationResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileSelect = (file) => {
    if (file && file.type === 'text/csv') {
      setSelectedFile(file);
      setValidationResult(null);
    } else {
      alert('Please select a valid CSV file');
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    handleFileSelect(file);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setDragOver(false);
    const file = event.dataTransfer.files[0];
    handleFileSelect(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      alert('Please select a file first');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setValidationResult(response.data);
      onUploadSuccess();
    } catch (error) {
      console.error('Upload error:', error);
      setValidationResult({
        status: 'error',
        errors: [{ row: 0, message: 'Failed to upload file. Please try again.' }],
      });
    } finally {
      setLoading(false);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current.click();
  };

  return (
    <div>
      <div
        className={`upload-area ${dragOver ? 'dragover' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <div className="upload-icon">📁</div>
        <h4>Drop your CSV file here or click to browse</h4>
        <p className="text-muted">
          {selectedFile ? `Selected: ${selectedFile.name}` : 'Supports: .csv files only'}
        </p>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".csv"
          className="file-input"
        />
      </div>

      {selectedFile && (
        <div className="text-center mt-3">
          <Button
            variant="primary"
            onClick={handleUpload}
            disabled={loading}
            className="btn-upload"
          >
            {loading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                Validating...
              </>
            ) : (
              'Upload & Validate'
            )}
          </Button>
        </div>
      )}

      {validationResult && (
        <div className="mt-4">
          {validationResult.status === 'success' ? (
            <Alert variant="success" className="success-alert">
              <h5>✅ Validation Successful!</h5>
              <p className="mb-0">
                File "{validationResult.filename}" has been validated successfully.
                <br />
                Total rows processed: {validationResult.totalRows}
              </p>
            </Alert>
          ) : (
            <div>
              <Alert variant="danger" className="error-alert">
                <h5>❌ Validation Failed</h5>
                <p className="mb-0">
                  Found {validationResult.errorCount} error(s) in {validationResult.totalRows} rows.
                </p>
              </Alert>

              {validationResult.errors && validationResult.errors.length > 0 && (
                <Table striped bordered hover className="error-table">
                  <thead className="table-dark">
                    <tr>
                      <th>Row</th>
                      <th>Error Message</th>
                    </tr>
                  </thead>
                  <tbody>
                    {validationResult.errors.map((error, index) => (
                      <tr key={index}>
                        <td>{error.row}</td>
                        <td>{error.message}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
