import React, { useState, useEffect } from 'react';
import { <PERSON>, Badge, Spinner, Alert } from 'react-bootstrap';
import axios from 'axios';

const UploadHistory = ({ refresh }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchHistory();
  }, [refresh]);

  const fetchHistory = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/history');
      setHistory(response.data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch history:', err);
      setError('Failed to load upload history');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusVariant = (status) => {
    return status === 'success' ? 'success' : 'danger';
  };

  if (loading) {
    return (
      <div className="text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-2">Loading upload history...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger">
        {error}
      </Alert>
    );
  }

  if (history.length === 0) {
    return (
      <Alert variant="info">
        No upload history found. Upload a CSV file to see it here.
      </Alert>
    );
  }

  return (
    <div>
      <h4 className="mb-4">Upload History</h4>
      {history.map((upload) => (
        <Card key={upload.id} className="history-card">
          <Card.Body>
            <div className="d-flex justify-content-between align-items-start">
              <div>
                <h6 className="mb-1">{upload.filename}</h6>
                <p className="text-muted mb-2">
                  Uploaded: {formatDate(upload.uploadTime)}
                </p>
                <div className="d-flex gap-3">
                  <small>
                    <strong>Total Rows:</strong> {upload.totalRows}
                  </small>
                  <small>
                    <strong>Errors:</strong> {upload.errorCount}
                  </small>
                </div>
              </div>
              <Badge 
                bg={getStatusVariant(upload.status)} 
                className="status-badge"
              >
                {upload.status.toUpperCase()}
              </Badge>
            </div>
            
            {upload.errorCount > 0 && upload.errors && (
              <div className="mt-3">
                <small className="text-muted">Recent Errors:</small>
                <div className="mt-1">
                  {JSON.parse(upload.errors).slice(0, 3).map((error, index) => (
                    <div key={index} className="small text-danger">
                      Row {error.row}: {error.message}
                    </div>
                  ))}
                  {JSON.parse(upload.errors).length > 3 && (
                    <div className="small text-muted">
                      ... and {JSON.parse(upload.errors).length - 3} more errors
                    </div>
                  )}
                </div>
              </div>
            )}
          </Card.Body>
        </Card>
      ))}
    </div>
  );
};

export default UploadHistory;
