import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Nav } from 'react-bootstrap';
import FileUpload from './components/FileUpload';
import UploadHistory from './components/UploadHistory';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('upload');
  const [refreshHistory, setRefreshHistory] = useState(false);

  const handleUploadSuccess = () => {
    setRefreshHistory(!refreshHistory);
  };

  return (
    <div className="App">
      <Container className="mt-4">
        <Row>
          <Col>
            <Card>
              <Card.Header>
                <h1 className="text-center mb-0">CSV Validator</h1>
                <p className="text-center text-muted mb-0">
                  Upload and validate your CSV files
                </p>
              </Card.Header>
              <Card.Body>
                <Nav variant="tabs" activeKey={activeTab} onSelect={setActiveTab} className="mb-4">
                  <Nav.Item>
                    <Nav.Link eventKey="upload">Upload CSV</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="history">Upload History</Nav.Link>
                  </Nav.Item>
                </Nav>

                {activeTab === 'upload' && (
                  <FileUpload onUploadSuccess={handleUploadSuccess} />
                )}
                
                {activeTab === 'history' && (
                  <UploadHistory refresh={refreshHistory} />
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}

export default App;
