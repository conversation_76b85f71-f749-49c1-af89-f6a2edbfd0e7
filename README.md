# CSV Validator - Full Stack Application

A full-stack web application built with **Spring Boot** (backend) and **React** (frontend) that allows users to upload CSV files, validate their content, and view validation results with error reporting.

## 🚀 Features

### Backend (Spring Boot)
- **REST API** with file upload endpoint (`POST /api/upload`)
- **CSV Validation** with detailed error reporting
- **PostgreSQL Integration** for storing upload history
- **CORS Configuration** for frontend connectivity
- **Comprehensive Error Handling**

### Frontend (React)
- **Drag & Drop File Upload** interface
- **Real-time Validation Results** display
- **Upload History** tracking
- **Responsive Design** with Bootstrap
- **Error Table** showing row-wise validation errors

### Validation Rules
- **Name**: Must not be empty
- **DateOfBirth**: 
  - Must not be empty
  - Must match format `yyyy-MM-dd` (e.g., 1990-05-15)
  - Must be a valid date (no impossible dates like Feb 30th)

## 📋 Prerequisites

### Backend Requirements
- **Java 17** or higher
- **Maven 3.6+**
- **PostgreSQL 12+**

### Frontend Requirements
- **Node.js 16+**
- **npm 8+**

## 🛠️ Setup Instructions

### 1. Database Setup

1. Install PostgreSQL and create a database:
```sql
CREATE DATABASE csvvalidator;
```

2. Update database credentials in `backend/src/main/resources/application.properties`:
```properties
spring.datasource.url=*********************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### 2. Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Install dependencies and run the application:
```bash
mvn clean install
mvn spring-boot:run
```

The backend will start on `http://localhost:8080`

**API Endpoints:**
- `POST /api/upload` - Upload and validate CSV file
- `GET /api/history` - Get upload history
- `GET /api/health` - Health check

### 3. Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

The frontend will start on `http://localhost:3000`

## 📁 Project Structure

```
csv-validator/
├── backend/                          # Spring Boot Backend
│   ├── src/main/java/com/csvvalidator/
│   │   ├── controller/               # REST Controllers
│   │   ├── service/                  # Business Logic
│   │   ├── entity/                   # JPA Entities
│   │   ├── repository/               # Data Access Layer
│   │   ├── dto/                      # Data Transfer Objects
│   │   └── config/                   # Configuration Classes
│   ├── src/main/resources/
│   │   └── application.properties    # App Configuration
│   └── pom.xml                       # Maven Dependencies
├── frontend/                         # React Frontend
│   ├── src/
│   │   ├── components/               # React Components
│   │   ├── App.js                    # Main App Component
│   │   └── index.js                  # Entry Point
│   ├── public/                       # Static Assets
│   └── package.json                  # NPM Dependencies
├── sample-data/                      # Test CSV Files
│   ├── valid-sample.csv              # Valid CSV for testing
│   └── invalid-sample.csv            # Invalid CSV for testing
└── README.md                         # This file
```

## 🧪 Testing

### Sample CSV Files

Two sample CSV files are provided in the `sample-data/` directory:

1. **valid-sample.csv** - Contains valid data for testing successful validation
2. **invalid-sample.csv** - Contains various validation errors for testing error handling

### Test the Application

1. Start both backend and frontend servers
2. Open `http://localhost:3000` in your browser
3. Upload the sample CSV files to test validation
4. Check the "Upload History" tab to see stored results

## 🔧 API Response Format

### Successful Validation
```json
{
  "status": "success",
  "errors": [],
  "filename": "valid-data.csv",
  "totalRows": 5,
  "errorCount": 0
}
```

### Failed Validation
```json
{
  "status": "error",
  "errors": [
    {
      "row": 3,
      "message": "Invalid date format. Expected yyyy-MM-dd"
    },
    {
      "row": 5,
      "message": "Name must not be empty"
    }
  ],
  "filename": "invalid-data.csv",
  "totalRows": 10,
  "errorCount": 2
}
```

## 🎨 UI Features

- **Modern Design** with gradient backgrounds and smooth animations
- **Drag & Drop Upload** with visual feedback
- **Responsive Layout** that works on all devices
- **Real-time Loading States** with spinners
- **Color-coded Status Badges** for easy identification
- **Tabbed Interface** for upload and history views

## 🚀 Production Deployment

### Backend
1. Build the JAR file: `mvn clean package`
2. Run with: `java -jar target/csv-validator-backend-0.0.1-SNAPSHOT.jar`
3. Configure production database settings

### Frontend
1. Build for production: `npm run build`
2. Serve the `build/` directory with a web server
3. Update API base URL for production backend

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

---

**Happy Coding! 🎉**
