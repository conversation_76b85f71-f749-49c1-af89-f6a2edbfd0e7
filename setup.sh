#!/bin/bash

echo "========================================"
echo "CSV Validator - Setup Script (Linux/Mac)"
echo "========================================"
echo

echo "Checking prerequisites..."
echo

# Check Java
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    echo "Please install Java 17 or higher"
    exit 1
fi
echo "✓ Java is installed"

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js 16 or higher"
    exit 1
fi
echo "✓ Node.js is installed"

# Check PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "WARNING: PostgreSQL is not installed or not in PATH"
    echo "Please make sure PostgreSQL is running on localhost:5432"
    echo
fi

echo
echo "Setting up backend..."
cd backend
./mvnw clean install
if [ $? -ne 0 ]; then
    echo "ERROR: Backend setup failed"
    exit 1
fi
echo "✓ Backend setup complete"

echo
echo "Setting up frontend..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Frontend setup failed"
    exit 1
fi
echo "✓ Frontend setup complete"

echo
echo "========================================"
echo "Setup completed successfully!"
echo "========================================"
echo
echo "To start the application:"
echo "1. Make sure PostgreSQL is running"
echo "2. Run: ./start-backend.sh"
echo "3. Run: ./start-frontend.sh"
echo "4. Open http://localhost:3000 in your browser"
echo
