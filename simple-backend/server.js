const express = require('express');
const multer = require('multer');
const csv = require('csv-parser');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

const app = express();
const port = 8080;

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/' });

// Validation function
function validateDate(dateString) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
        return false;
    }
    
    const date = new Date(dateString);
    const [year, month, day] = dateString.split('-').map(Number);
    
    return date.getFullYear() === year && 
           date.getMonth() === month - 1 && 
           date.getDate() === day;
}

// CSV validation endpoint
app.post('/api/upload', upload.single('file'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({
            status: 'error',
            errors: [{ row: 0, message: 'No file uploaded' }]
        });
    }

    const errors = [];
    let totalRows = 0;
    const filePath = req.file.path;
    const filename = req.file.originalname;

    fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
            totalRows++;
            const rowNumber = totalRows + 1; // +1 for header row

            // Validate Name
            if (!row.Name || row.Name.trim() === '') {
                errors.push({
                    row: rowNumber,
                    message: 'Name must not be empty'
                });
            }

            // Validate DateOfBirth
            if (!row.DateOfBirth || row.DateOfBirth.trim() === '') {
                errors.push({
                    row: rowNumber,
                    message: 'DateOfBirth must not be empty'
                });
            } else if (!validateDate(row.DateOfBirth.trim())) {
                errors.push({
                    row: rowNumber,
                    message: 'Invalid date format. Expected yyyy-MM-dd'
                });
            }
        })
        .on('end', () => {
            // Clean up uploaded file
            fs.unlinkSync(filePath);

            const status = errors.length === 0 ? 'success' : 'error';
            res.json({
                status,
                errors,
                filename,
                totalRows,
                errorCount: errors.length
            });
        })
        .on('error', (error) => {
            console.error('CSV parsing error:', error);
            res.status(500).json({
                status: 'error',
                errors: [{ row: 0, message: 'Failed to parse CSV file' }]
            });
        });
});

// History endpoint (simplified)
app.get('/api/history', (req, res) => {
    res.json([]);
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ message: 'CSV Validator API is running!' });
});

// Create uploads directory if it doesn't exist
if (!fs.existsSync('uploads')) {
    fs.mkdirSync('uploads');
}

app.listen(port, () => {
    console.log(`🚀 CSV Validator Backend running on http://localhost:${port}`);
    console.log(`📁 Upload endpoint: http://localhost:${port}/api/upload`);
    console.log(`📊 Health check: http://localhost:${port}/api/health`);
});
