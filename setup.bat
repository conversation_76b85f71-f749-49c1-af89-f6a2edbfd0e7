@echo off
echo ========================================
echo CSV Validator - Setup Script (Windows)
echo ========================================
echo.

echo Checking prerequisites...
echo.

:: Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 17 or higher
    pause
    exit /b 1
)
echo ✓ Java is installed

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 16 or higher
    pause
    exit /b 1
)
echo ✓ Node.js is installed

:: Check PostgreSQL
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: PostgreSQL is not installed or not in PATH
    echo Please make sure PostgreSQL is running on localhost:5432
    echo.
)

echo.
echo Setting up backend...
cd backend
call mvn clean install
if %errorlevel% neq 0 (
    echo ERROR: Backend setup failed
    pause
    exit /b 1
)
echo ✓ Backend setup complete

echo.
echo Setting up frontend...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Frontend setup failed
    pause
    exit /b 1
)
echo ✓ Frontend setup complete

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To start the application:
echo 1. Make sure PostgreSQL is running
echo 2. Run: start-backend.bat
echo 3. Run: start-frontend.bat
echo 4. Open http://localhost:3000 in your browser
echo.
pause
