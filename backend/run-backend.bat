@echo off
echo Starting CSV Validator Backend...
echo.

REM Set JAVA_HOME to a common Java installation path
set JAVA_HOME=C:\Program Files\Common Files\Oracle\Java\javapath
echo Using JAVA_HOME: %JAVA_HOME%
echo.

REM Try to run with local Maven
if exist apache-maven-3.9.5\bin\mvn.cmd (
    echo Running with local Maven...
    apache-maven-3.9.5\bin\mvn.cmd clean compile spring-boot:run
) else (
    echo Local Maven not found. Trying Maven wrapper...
    if exist mvnw.cmd (
        mvnw.cmd spring-boot:run
    ) else (
        echo No Maven found. Please install Maven manually.
        echo.
        echo Alternative: Try running with Docker:
        echo docker-compose up --build
        pause
    )
)
