package com.csvvalidator.dto;

import java.util.List;

public class ValidationResponse {
    private String status;
    private List<ValidationError> errors;
    private String filename;
    private Integer totalRows;
    private Integer errorCount;

    public ValidationResponse() {}

    public ValidationResponse(String status, List<ValidationError> errors) {
        this.status = status;
        this.errors = errors;
    }

    public ValidationResponse(String status, List<ValidationError> errors, String filename,
                            Integer totalRows, Integer errorCount) {
        this.status = status;
        this.errors = errors;
        this.filename = filename;
        this.totalRows = totalRows;
        this.errorCount = errorCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<ValidationError> getErrors() {
        return errors;
    }

    public void setErrors(List<ValidationError> errors) {
        this.errors = errors;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Integer getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(Integer totalRows) {
        this.totalRows = totalRows;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }
}
