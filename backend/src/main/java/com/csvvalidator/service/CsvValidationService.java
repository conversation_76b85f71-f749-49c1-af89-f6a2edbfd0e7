package com.csvvalidator.service;

import com.csvvalidator.dto.ValidationError;
import com.csvvalidator.dto.ValidationResponse;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

@Service
public class CsvValidationService {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public ValidationResponse validateCsvFile(MultipartFile file) throws IOException {
        List<ValidationError> errors = new ArrayList<>();
        int totalRows = 0;
        String filename = file.getOriginalFilename();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            for (CSVRecord csvRecord : csvParser) {
                totalRows++;
                int rowNumber = (int) csvRecord.getRecordNumber() + 1; // +1 because header is row 1

                // Validate Name field
                String name = csvRecord.get("Name");
                if (name == null || name.trim().isEmpty()) {
                    errors.add(new ValidationError(rowNumber, "Name must not be empty"));
                }

                // Validate DateOfBirth field
                String dateOfBirth = csvRecord.get("DateOfBirth");
                if (dateOfBirth == null || dateOfBirth.trim().isEmpty()) {
                    errors.add(new ValidationError(rowNumber, "DateOfBirth must not be empty"));
                } else {
                    validateDateFormat(dateOfBirth.trim(), rowNumber, errors);
                }
            }
        }

        String status = errors.isEmpty() ? "success" : "error";
        ValidationResponse response = new ValidationResponse(status, errors, filename, totalRows, errors.size());

        return response;
    }

    private void validateDateFormat(String dateString, int rowNumber, List<ValidationError> errors) {
        try {
            LocalDate.parse(dateString, dateFormatter);

            // Additional validation for impossible dates
            String[] parts = dateString.split("-");
            if (parts.length == 3) {
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);

                // Check for impossible dates like February 30th
                if (month == 2 && day > 29) {
                    errors.add(new ValidationError(rowNumber, "Invalid date: February cannot have more than 29 days"));
                } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
                    errors.add(new ValidationError(rowNumber, "Invalid date: This month cannot have more than 30 days"));
                } else if (day > 31) {
                    errors.add(new ValidationError(rowNumber, "Invalid date: Day cannot be greater than 31"));
                }
            }
        } catch (DateTimeParseException e) {
            errors.add(new ValidationError(rowNumber, "Invalid date format. Expected yyyy-MM-dd"));
        } catch (NumberFormatException e) {
            errors.add(new ValidationError(rowNumber, "Invalid date format. Expected yyyy-MM-dd"));
        }
    }
}
