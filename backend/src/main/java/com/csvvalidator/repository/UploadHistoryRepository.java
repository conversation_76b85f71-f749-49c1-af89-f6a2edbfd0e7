package com.csvvalidator.repository;

import com.csvvalidator.entity.UploadHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UploadHistoryRepository extends JpaRepository<UploadHistory, Long> {
    
    @Query("SELECT u FROM UploadHistory u ORDER BY u.uploadTime DESC")
    List<UploadHistory> findAllOrderByUploadTimeDesc();
    
    List<UploadHistory> findByStatus(String status);
}
