package com.csvvalidator.controller;

import com.csvvalidator.dto.ValidationResponse;
import com.csvvalidator.service.CsvValidationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "http://localhost:3000")
public class CsvController {

    @Autowired
    private CsvValidationService csvValidationService;

    @PostMapping("/upload")
    public ResponseEntity<ValidationResponse> uploadCsvFile(@RequestParam("file") MultipartFile file) {
        try {
            // Validate file type
            if (file.isEmpty()) {
                ValidationResponse response = new ValidationResponse();
                response.setStatus("error");
                response.setErrors(List.of());
                return ResponseEntity.badRequest().body(response);
            }

            String filename = file.getOriginalFilename();
            if (filename == null || !filename.toLowerCase().endsWith(".csv")) {
                ValidationResponse response = new ValidationResponse();
                response.setStatus("error");
                response.setErrors(List.of());
                return ResponseEntity.badRequest().body(response);
            }

            ValidationResponse response = csvValidationService.validateCsvFile(file);
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            ValidationResponse response = new ValidationResponse();
            response.setStatus("error");
            response.setErrors(List.of());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/history")
    public ResponseEntity<String> getUploadHistory() {
        // Simplified response for now without database
        return ResponseEntity.ok("Upload history feature temporarily disabled");
    }

    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("CSV Validator API is running!");
    }
}
