package com.csvvalidator.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "upload_history")
public class UploadHistory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "filename", nullable = false)
    private String filename;
    
    @Column(name = "upload_time", nullable = false)
    private LocalDateTime uploadTime;
    
    @Column(name = "total_rows")
    private Integer totalRows;
    
    @Column(name = "error_count")
    private Integer errorCount;
    
    @Column(name = "status", nullable = false)
    private String status;
    
    @Column(name = "errors", columnDefinition = "TEXT")
    private String errors; // JSON string of errors
    
    public UploadHistory() {}
    
    public UploadHistory(String filename, LocalDateTime uploadTime, Integer totalRows, 
                        Integer errorCount, String status, String errors) {
        this.filename = filename;
        this.uploadTime = uploadTime;
        this.totalRows = totalRows;
        this.errorCount = errorCount;
        this.status = status;
        this.errors = errors;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFilename() {
        return filename;
    }
    
    public void setFilename(String filename) {
        this.filename = filename;
    }
    
    public LocalDateTime getUploadTime() {
        return uploadTime;
    }
    
    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }
    
    public Integer getTotalRows() {
        return totalRows;
    }
    
    public void setTotalRows(Integer totalRows) {
        this.totalRows = totalRows;
    }
    
    public Integer getErrorCount() {
        return errorCount;
    }
    
    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getErrors() {
        return errors;
    }
    
    public void setErrors(String errors) {
        this.errors = errors;
    }
}
