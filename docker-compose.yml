version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: csvvalidator-postgres
    environment:
      POSTGRES_DB: csvvalidator
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - csvvalidator-network

  # Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: csvvalidator-backend
    environment:
      SPRING_DATASOURCE_URL: ********************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: password
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - csvvalidator-network

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: csvvalidator-frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - csvvalidator-network

volumes:
  postgres_data:

networks:
  csvvalidator-network:
    driver: bridge
