-- CSV Validator Database Initialization Script
-- Run this script to set up the PostgreSQL database

-- Create database (run this as postgres superuser)
-- CREATE DATABASE csvvalidator;

-- Connect to the csvvalidator database and run the following:

-- The upload_history table will be created automatically by Spring Boot JPA
-- when the application starts (due to spring.jpa.hibernate.ddl-auto=update)

-- However, if you want to create it manually, here's the schema:

/*
CREATE TABLE upload_history (
    id BIGSERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    upload_time TIMESTAMP NOT NULL,
    total_rows INTEGER,
    error_count INTEGER,
    status VARCHAR(50) NOT NULL,
    errors TEXT
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_upload_history_upload_time ON upload_history(upload_time DESC);
CREATE INDEX idx_upload_history_status ON upload_history(status);
*/

-- Sample data (optional)
-- This will be populated automatically when you use the application
